/*
 * @Description: JWT认证组件默认配置
 * @Date: 2025-05-15
 */
import { MidwayConfig } from '@midwayjs/core';

export default {
  // JWT认证组件配置
  jwtAuth: {
    // 是否启用
    enable: true,
    // 白名单路径
    whitelist: [
      '/auth/login',
      '/auth/register',
      '/auth/refresh-token',
      '/public',
      '/openapi',
      '/dev-tools',
      '/api/sso', // SSO相关接口
    ],
    // 令牌刷新阈值（秒），默认提前30分钟刷新
    refreshThreshold: 30 * 60,

    // API管理器配置
    apiManagerBaseURL: 'http://127.0.0.1:1002', // API管理器基础URL
    apiPath: '/apis/request', // API请求路径
    postByUserCodesApiCode: 'postByUserCodes', // 获取用户信息的API编码
    getDataConverApiCode: 'getDataConver', // 解密信息的API编码
    apiTimeout: 10000, // API请求超时时间（毫秒）
  },

  // Axios配置
  axios: {
    default: {
      timeout: 10000, // 默认超时时间
    },
  },
} as MidwayConfig;
