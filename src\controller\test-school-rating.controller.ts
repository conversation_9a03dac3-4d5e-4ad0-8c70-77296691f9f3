import { Controller, Get, Post, Inject, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ApiResponseUtil } from '../common/ApiResponse';

@Controller('/api/test-school-rating')
export class TestSchoolRatingController {
  @Inject()
  ctx: Context;

  /**
   * 测试学校评分验证 - 100分制
   */
  @Post('/validate')
  async validateSchoolRating(@Body() body: { school_rating: number }) {
    try {
      const { school_rating } = body;
      
      this.ctx.logger.info('测试学校评分验证', { school_rating });
      
      // 验证评分范围
      const validationResult = {
        school_rating,
        is_valid: school_rating >= 0 && school_rating <= 100,
        validation_rules: {
          min_score: 0,
          max_score: 100,
          description: '学校评分采用100分制，范围为0-100分'
        }
      };
      
      if (!validationResult.is_valid) {
        return ApiResponseUtil.error(400, '学校评分必须在0-100分之间');
      }
      
      // 评分等级划分
      let grade = '';
      let description = '';
      
      if (school_rating >= 90) {
        grade = 'A+';
        description = '优秀';
      } else if (school_rating >= 80) {
        grade = 'A';
        description = '良好';
      } else if (school_rating >= 70) {
        grade = 'B';
        description = '中等';
      } else if (school_rating >= 60) {
        grade = 'C';
        description = '及格';
      } else {
        grade = 'D';
        description = '不及格';
      }
      
      const result = {
        ...validationResult,
        grade_info: {
          grade,
          description,
          score_range: getScoreRange(grade)
        }
      };
      
      this.ctx.logger.info('学校评分验证成功', {
        school_rating,
        grade,
        description
      });
      
      return ApiResponseUtil.success(result, '学校评分验证成功');
    } catch (error) {
      this.ctx.logger.error('学校评分验证失败', {
        error: error.message
      });
      
      return ApiResponseUtil.error(400, error.message);
    }
  }

  /**
   * 获取学校评分说明
   */
  @Get('/info')
  async getSchoolRatingInfo() {
    const ratingInfo = {
      system: '100分制',
      description: '学校评分采用100分制，范围为0-100分',
      grade_levels: [
        { grade: 'A+', range: '90-100分', description: '优秀' },
        { grade: 'A', range: '80-89分', description: '良好' },
        { grade: 'B', range: '70-79分', description: '中等' },
        { grade: 'C', range: '60-69分', description: '及格' },
        { grade: 'D', range: '0-59分', description: '不及格' }
      ],
      validation_rules: {
        min_value: 0,
        max_value: 100,
        data_type: 'integer',
        required: false
      },
      examples: [
        { score: 95, grade: 'A+', description: '学校管理优秀，教学质量很高' },
        { score: 85, grade: 'A', description: '学校整体表现良好' },
        { score: 75, grade: 'B', description: '学校表现中等，有改进空间' },
        { score: 65, grade: 'C', description: '学校基本达标' },
        { score: 45, grade: 'D', description: '学校需要大幅改进' }
      ]
    };
    
    return ApiResponseUtil.success(ratingInfo, '获取学校评分说明成功');
  }

  /**
   * 对比新旧评分系统
   */
  @Get('/comparison')
  async getSystemComparison() {
    const comparison = {
      old_system: {
        name: '星级制',
        range: '1-10星',
        description: '使用星级评分，需要转换为百分制',
        conversion_rules: {
          '5_star_mode': '1星=20分, 2星=40分, 3星=60分, 4星=80分, 5星=100分',
          '10_star_mode': '1星=10分, 2星=20分, ..., 10星=100分'
        }
      },
      new_system: {
        name: '100分制',
        range: '0-100分',
        description: '直接使用百分制评分，更直观准确',
        advantages: [
          '评分更精确，支持0-100任意整数',
          '无需转换，直接显示百分制分数',
          '与教师评分统一，便于对比分析',
          '符合常见的评分习惯'
        ]
      },
      migration_notes: [
        '现有星级数据会自动转换为百分制',
        '5星制：星级 × 20 = 百分制分数',
        '10星制：星级 × 10 = 百分制分数',
        '新提交的评分直接使用100分制'
      ]
    };
    
    return ApiResponseUtil.success(comparison, '获取评分系统对比成功');
  }
}

/**
 * 根据等级获取分数范围
 */
function getScoreRange(grade: string): string {
  const ranges: { [key: string]: string } = {
    'A+': '90-100分',
    'A': '80-89分',
    'B': '70-79分',
    'C': '60-69分',
    'D': '0-59分'
  };
  return ranges[grade] || '未知';
}
