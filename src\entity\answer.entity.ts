import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Response } from './response.entity';

@Table({
  tableName: 'answers',
  comment: '答案表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_answer_response',
      fields: ['response_id'],
    },
    {
      name: 'idx_answer_teacher',
      fields: ['sso_teacher_id'],
    },
    {
      name: 'idx_answer_rating',
      fields: ['rating'],
    },
    {
      // 唯一键：响应ID+教师ID（一个响应中每个教师只能评价一次）
      name: 'uk_answer_response_teacher',
      fields: ['response_id', 'sso_teacher_id'],
      unique: true,
    },
  ],
})
export class Answer extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '答案ID',
  })
  id?: number = undefined;

  @ForeignKey(() => Response)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '响应ID',
  })
  response_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO教师ID',
  })
  sso_teacher_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师姓名',
  })
  sso_teacher_name: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师所属科目',
  })
  sso_teacher_subject: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师职位',
  })
  sso_teacher_position: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师部门',
  })
  sso_teacher_department: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    validate: {
      min: 0,
      max: 100, // 100分制
    },
    comment: '评分',
  })
  rating: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '文字描述/评价内容',
  })
  description: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '评价标签（JSON格式存储多个标签）',
  })
  tags: string[];

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    comment: '是否推荐该教师',
  })
  is_recommended: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100,
    },
    comment: '教学质量评分（细分评价）',
  })
  teaching_quality_rating: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100,
    },
    comment: '教学态度评分（细分评价）',
  })
  teaching_attitude_rating: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100,
    },
    comment: '课堂管理评分（细分评价）',
  })
  classroom_management_rating: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100,
    },
    comment: '沟通能力评分（细分评价）',
  })
  communication_rating: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100,
    },
    comment: '专业知识评分（细分评价）',
  })
  professional_knowledge_rating: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '改进建议',
  })
  improvement_suggestions: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '最满意的方面',
  })
  most_satisfied_aspect: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '需要改进的方面',
  })
  needs_improvement_aspect: string;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => Response)
  response: Response;
}
