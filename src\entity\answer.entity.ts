import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';

@Table({
  tableName: 'answers',
  comment: '答案表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_answer_response',
      fields: ['response_id'],
    },
    {
      name: 'idx_answer_teacher',
      fields: ['sso_teacher_id'],
    },
    {
      name: 'idx_answer_rating',
      fields: ['rating'],
    },
    {
      // 唯一键：响应ID+教师ID（一个响应中每个教师只能评价一次）
      name: 'uk_answer_response_teacher',
      fields: ['response_id', 'sso_teacher_id'],
      unique: true,
    },
  ],
})
export class Answer extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '答案ID',
  })
  id?: number = undefined;

  @ForeignKey(() => require('./response.entity').Response)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '响应ID',
  })
  response_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO教师ID',
  })
  sso_teacher_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师姓名',
  })
  sso_teacher_name: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师所属科目',
  })
  sso_teacher_subject: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师职位',
  })
  sso_teacher_position: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO教师部门',
  })
  sso_teacher_department: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    validate: {
      min: 0,
      max: 100, // 100分制
    },
    comment: '评分',
  })
  rating: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '文字描述/评价内容',
  })
  description: string;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => require('./response.entity').Response)
  response: any;
}
