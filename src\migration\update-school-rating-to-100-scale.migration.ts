import { QueryInterface, DataTypes } from 'sequelize';

export default {
  async up(queryInterface: QueryInterface): Promise<void> {
    // 首先备份现有数据（将星级转换为百分制）
    // 假设原来是10星制，需要将星级乘以10转换为百分制
    await queryInterface.sequelize.query(`
      UPDATE responses 
      SET school_rating = CASE 
        WHEN school_rating IS NOT NULL AND school_rating <= 10 THEN school_rating * 10
        ELSE school_rating 
      END
      WHERE school_rating IS NOT NULL AND school_rating <= 10;
    `);

    // 更新字段注释
    await queryInterface.changeColumn('responses', 'school_rating', {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '学校评分（0-100分制）',
    });

    // 添加检查约束确保评分在0-100范围内
    await queryInterface.sequelize.query(`
      ALTER TABLE responses
      ADD CONSTRAINT chk_school_rating_range
      CHECK (school_rating IS NULL OR (school_rating >= 0 AND school_rating <= 100))
    `);
  },

  async down(queryInterface: QueryInterface): Promise<void> {
    // 删除检查约束
    await queryInterface.sequelize.query(`
      ALTER TABLE responses DROP CONSTRAINT chk_school_rating_range
    `);

    // 将百分制转换回星级制（除以10）
    await queryInterface.sequelize.query(`
      UPDATE responses 
      SET school_rating = CASE 
        WHEN school_rating IS NOT NULL AND school_rating > 10 THEN ROUND(school_rating / 10)
        ELSE school_rating 
      END
      WHERE school_rating IS NOT NULL;
    `);

    // 恢复原来的字段注释
    await queryInterface.changeColumn('responses', 'school_rating', {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '学校评分（如果问卷包含学校评价）',
    });
  },
};
