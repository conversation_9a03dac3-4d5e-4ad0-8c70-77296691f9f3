import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Custome } from './api_sso/custome.service';
import {
  IParentPhoneVerifyResponse,
  IStudentQuestionnairesResponse,
} from '../dto/parent.dto';
import { CustomError } from '../error/custom.error';
import {
  Questionnaire,
  QuestionnaireStatus,
} from '../entity/questionnaire.entity';
import { Response } from '../entity/response.entity';

@Provide()
export class ParentService {
  @Inject()
  custome: Custome;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  /**
   * 验证家长手机号
   * 通过查询该手机号下的学生信息来验证手机号是否有效
   * @param phone 家长手机号
   * @returns 验证结果
   */
  async verifyParentPhone(phone: string): Promise<IParentPhoneVerifyResponse> {
    try {
      // 调用SSO接口获取该手机号下的学生信息
      const parent = await this.custome.getChildren(phone);

      if (!parent) {
        return {
          is_valid: false,
          message: '手机号验证失败，请确认手机号是否正确',
        };
      }

      return {
        is_valid: true,
        message: '验证成功',
        parent,
      };
    } catch (error) {
      // 如果是网络错误或SSO接口错误，返回验证失败
      if (error.message && error.message.includes('网络')) {
        throw new CustomError('网络连接异常，请稍后重试');
      }

      // 其他错误也认为是验证失败
      return {
        is_valid: false,
        message: '手机号验证失败，请确认手机号是否正确',
      };
    }
  }

  /**
   * 查询学生可填写的问卷
   * @param ssoSchoolCode 学校编码
   * @param ssoStudentId 学生ID
   * @param parentPhone 家长手机号（可选，用于检查是否已提交）
   * @returns 问卷查询结果
   */
  async getStudentQuestionnaires(
    ssoSchoolCode: string,
    ssoStudentId: string,
    parentPhone?: string
  ): Promise<IStudentQuestionnairesResponse> {
    try {
      // 1. 查询该学校的已发布问卷（同一学校只能有一个发布状态的问卷）
      const questionnaire = await this.questionnaireRepository.findOne({
        where: {
          sso_school_code: ssoSchoolCode,
          status: QuestionnaireStatus.PUBLISHED,
        },
      });

      // 如果没有找到问卷
      if (!questionnaire) {
        return {
          has_questionnaire: false,
          message: '该学校暂无可填写的问卷',
        };
      }

      // 2. 检查是否已提交（如果提供了家长手机号）
      let isSubmitted = false;
      if (parentPhone) {
        const existingResponse = await this.responseRepository.findOne({
          where: {
            questionnaire_id: questionnaire.id,
            sso_student_id: ssoStudentId,
            parent_phone: parentPhone,
            month: questionnaire.month, // 使用问卷的月份
          },
        });
        isSubmitted = !!existingResponse;
      }

      // 3. 返回问卷信息
      return {
        has_questionnaire: true,
        message: isSubmitted ? '该学生的问卷已提交' : '找到可填写的问卷',
        questionnaire: {
          id: questionnaire.id,
          title: questionnaire.title,
          description: questionnaire.description,
          month: questionnaire.month,
          star_mode: questionnaire.star_mode,
          sso_school_code: questionnaire.sso_school_code,
          sso_school_name: questionnaire.sso_school_name,
          start_time: questionnaire.start_time,
          end_time: questionnaire.end_time,
          instructions: questionnaire.instructions,
          max_teachers_limit: questionnaire.max_teachers_limit,
          is_submitted: isSubmitted,
        },
      };
    } catch (error) {
      throw new CustomError('查询学生问卷时发生错误：' + error.message);
    }
  }
}
