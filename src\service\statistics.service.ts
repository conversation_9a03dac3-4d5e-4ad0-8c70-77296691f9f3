import { Provide } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize, QueryTypes } from 'sequelize';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
} from '../dto/statistics.dto';

import {
  ISchoolStatistics,
  ITeacherStatistics,
  ITeacherRankingResponse,
} from '../interface';

@Provide()
export class StatisticsService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectDataSource()
  sequelize: Sequelize;

  /**
   * 获取学校维度统计
   * @param queryDto 查询条件
   * @returns 学校统计数据
   */
  async getSchoolStatistics(
    queryDto: SchoolStatisticsQueryDTO
  ): Promise<ISchoolStatistics> {
    const {
      sso_school_code,
      month,
      start_month,
      end_month,
      include_trend,
      include_teacher_ranking,
    } = queryDto;

    // 构建基础查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        q.sso_school_code,
        q.sso_school_name,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) as completed_responses,
        ROUND(
          COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) * 100.0 /
          NULLIF(COUNT(DISTINCT r.id), 0), 2
        ) as completion_rate,
        ROUND(AVG(r.school_rating), 2) as school_average_score,
        ROUND(AVG(r.total_average_score), 2) as teacher_average_score,
        COUNT(DISTINCT a.sso_teacher_id) as total_teachers_evaluated
      FROM questionnaires q
      LEFT JOIN responses r ON q.id = r.questionnaire_id
      LEFT JOIN answers a ON r.id = a.response_id
      WHERE ${whereClause}
      GROUP BY q.sso_school_code, q.sso_school_name
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该学校的统计数据');
    }

    const result: ISchoolStatistics = {
      sso_school_code: basicStats.sso_school_code,
      sso_school_name: basicStats.sso_school_name,
      month: month,
      total_responses: parseInt(basicStats.total_responses) || 0,
      completed_responses: parseInt(basicStats.completed_responses) || 0,
      completion_rate: parseFloat(basicStats.completion_rate) || 0,
      school_average_score: parseFloat(basicStats.school_average_score) || 0,
      teacher_average_score: parseFloat(basicStats.teacher_average_score) || 0,
      total_teachers_evaluated:
        parseInt(basicStats.total_teachers_evaluated) || 0,
    };

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.response_trend = await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }

    // 如果需要教师排名
    if (include_teacher_ranking) {
      const rankingResult = await this.getTeacherRanking({
        sso_school_code,
        month,
        page: 1,
        limit: 10,
        sort_by: 'average_score',
        sort_order: 'DESC',
      });
      result.teacher_ranking = rankingResult.list;
    }

    return result;
  }

  /**
   * 获取教师维度统计
   * @param queryDto 查询条件
   * @returns 教师统计数据
   */
  async getTeacherStatistics(
    queryDto: TeacherStatisticsQueryDTO
  ): Promise<ITeacherStatistics> {
    const {
      sso_teacher_id,
      sso_school_code,
      month,
      start_month,
      end_month,
      include_distribution,
      include_keywords,
      include_trend,
    } = queryDto;

    // 构建查询条件
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id };

    if (sso_school_code) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = sso_school_code;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as total_evaluations,
        ROUND(AVG(CASE WHEN q.star_mode = 5 THEN a.rating * 20
                       ELSE a.rating * 10 END), 2) as average_score,
        ROUND(COUNT(CASE WHEN a.is_recommended = 1 THEN 1 END) * 100.0 /
              NULLIF(COUNT(a.id), 0), 2) as recommendation_rate,
        ROUND(AVG(CASE WHEN a.teaching_quality_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN a.teaching_quality_rating * 20
               ELSE a.teaching_quality_rating * 10 END
        END), 2) as avg_teaching_quality,
        ROUND(AVG(CASE WHEN a.teaching_attitude_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN a.teaching_attitude_rating * 20
               ELSE a.teaching_attitude_rating * 10 END
        END), 2) as avg_teaching_attitude,
        ROUND(AVG(CASE WHEN a.classroom_management_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN a.classroom_management_rating * 20
               ELSE a.classroom_management_rating * 10 END
        END), 2) as avg_classroom_management,
        ROUND(AVG(CASE WHEN a.communication_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN a.communication_rating * 20
               ELSE a.communication_rating * 10 END
        END), 2) as avg_communication,
        ROUND(AVG(CASE WHEN a.professional_knowledge_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN a.professional_knowledge_rating * 20
               ELSE a.professional_knowledge_rating * 10 END
        END), 2) as avg_professional_knowledge
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该教师的统计数据');
    }

    const result: ITeacherStatistics = {
      sso_teacher_id: basicStats.sso_teacher_id,
      sso_teacher_name: basicStats.sso_teacher_name,
      sso_teacher_subject: basicStats.sso_teacher_subject,
      sso_teacher_department: basicStats.sso_teacher_department,
      month: month,
      total_evaluations: parseInt(basicStats.total_evaluations) || 0,
      average_score: parseFloat(basicStats.average_score) || 0,
      recommendation_rate: parseFloat(basicStats.recommendation_rate) || 0,
      detailed_scores: {
        teaching_quality: parseFloat(basicStats.avg_teaching_quality) || 0,
        teaching_attitude: parseFloat(basicStats.avg_teaching_attitude) || 0,
        classroom_management:
          parseFloat(basicStats.avg_classroom_management) || 0,
        communication: parseFloat(basicStats.avg_communication) || 0,
        professional_knowledge:
          parseFloat(basicStats.avg_professional_knowledge) || 0,
      },
    };

    // 如果需要评分分布
    if (include_distribution) {
      result.score_distribution = await this.getScoreDistribution(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要关键词云
    if (include_keywords) {
      result.keyword_cloud = await this.getKeywordCloud(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.evaluation_trend = await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    }

    return result;
  }

  /**
   * 获取教师排名
   * @param queryDto 查询条件
   * @returns 教师排名列表
   */
  async getTeacherRanking(
    queryDto: TeacherRankingQueryDTO
  ): Promise<ITeacherRankingResponse> {
    const {
      sso_school_code,
      month,
      subject,
      department,
      page = 1,
      limit = 20,
      sort_by = 'average_score',
      sort_order = 'DESC',
    } = queryDto;

    // 构建查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    if (subject) {
      whereClause += ' AND a.sso_teacher_subject = :subject';
      replacements.subject = subject;
    }

    if (department) {
      whereClause += ' AND a.sso_teacher_department = :department';
      replacements.department = department;
    }

    // 排序字段映射
    const sortFieldMap = {
      average_score: 'average_score',
      evaluation_count: 'evaluation_count',
      recommendation_rate: 'recommendation_rate',
    };

    const sortField = sortFieldMap[sort_by] || 'average_score';

    const rankingQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(CASE WHEN q.star_mode = 5 THEN a.rating * 20
                       ELSE a.rating * 10 END), 2) as average_score,
        ROUND(COUNT(CASE WHEN a.is_recommended = 1 THEN 1 END) * 100.0 /
              NULLIF(COUNT(a.id), 0), 2) as recommendation_rate
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
      ORDER BY ${sortField} ${sort_order}
      LIMIT :limit OFFSET :offset
    `;

    const offset = (page - 1) * limit;
    replacements.limit = limit;
    replacements.offset = offset;

    const rankings = (await this.sequelize.query(rankingQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT a.sso_teacher_id) as total
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [countResult] = (await this.sequelize.query(countQuery, {
      type: QueryTypes.SELECT,
      replacements: { sso_school_code, month, subject, department },
    })) as any[];

    const list = rankings.map((item, index) => ({
      sso_teacher_id: item.sso_teacher_id,
      sso_teacher_name: item.sso_teacher_name,
      sso_teacher_subject: item.sso_teacher_subject,
      sso_teacher_department: item.sso_teacher_department,
      average_score: parseFloat(item.average_score) || 0,
      evaluation_count: parseInt(item.evaluation_count) || 0,
      recommendation_rate: parseFloat(item.recommendation_rate) || 0,
      rank: offset + index + 1,
    }));

    return {
      list,
      total: parseInt(countResult.total) || 0,
    };
  }

  /**
   * 获取响应趋势数据
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 趋势数据
   */
  async getResponseTrend(
    ssoSchoolId: string,
    startMonth: string,
    endMonth: string
  ): Promise<any[]> {
    const trendQuery = `
      SELECT
        r.month,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) as completed_responses,
        ROUND(AVG(CASE WHEN r.school_rating IS NOT NULL THEN
          CASE WHEN q.star_mode = 5 THEN r.school_rating * 20
               ELSE r.school_rating * 10 END
        END), 2) as avg_school_score,
        ROUND(AVG(r.total_average_score), 2) as avg_teacher_score
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE q.sso_school_code = :sso_school_code
        AND r.month BETWEEN :start_month AND :end_month
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements: {
        sso_school_code: ssoSchoolId,
        start_month: startMonth,
        end_month: endMonth,
      },
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      total_responses: parseInt(item.total_responses) || 0,
      completed_responses: parseInt(item.completed_responses) || 0,
      completion_rate:
        item.total_responses > 0
          ? Math.round(
              (item.completed_responses / item.total_responses) * 100 * 100
            ) / 100
          : 0,
      avg_school_score: parseFloat(item.avg_school_score) || 0,
      avg_teacher_score: parseFloat(item.avg_teacher_score) || 0,
    }));
  }

  /**
   * 获取教师趋势数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 教师趋势数据
   */
  async getTeacherTrend(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    startMonth?: string,
    endMonth?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (startMonth && endMonth) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = startMonth;
      replacements.end_month = endMonth;
    }

    const trendQuery = `
      SELECT
        r.month,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(CASE WHEN q.star_mode = 5 THEN a.rating * 20
                       ELSE a.rating * 10 END), 2) as average_score,
        ROUND(COUNT(CASE WHEN a.is_recommended = 1 THEN 1 END) * 100.0 /
              NULLIF(COUNT(a.id), 0), 2) as recommendation_rate
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      evaluation_count: parseInt(item.evaluation_count) || 0,
      average_score: parseFloat(item.average_score) || 0,
      recommendation_rate: parseFloat(item.recommendation_rate) || 0,
    }));
  }

  /**
   * 获取教师评分分布
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 评分分布
   */
  async getScoreDistribution(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 先获取总数
    const totalCountQuery = `
      SELECT COUNT(*) as total_count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [totalResult] = (await this.sequelize.query(totalCountQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    const totalCount = parseInt(totalResult?.total_count) || 0;

    if (totalCount === 0) {
      return [];
    }

    const distributionQuery = `
      SELECT
        CASE
          WHEN (CASE WHEN q.star_mode = 5 THEN a.rating * 20 ELSE a.rating * 10 END) >= 90 THEN '90-100'
          WHEN (CASE WHEN q.star_mode = 5 THEN a.rating * 20 ELSE a.rating * 10 END) >= 80 THEN '80-89'
          WHEN (CASE WHEN q.star_mode = 5 THEN a.rating * 20 ELSE a.rating * 10 END) >= 70 THEN '70-79'
          WHEN (CASE WHEN q.star_mode = 5 THEN a.rating * 20 ELSE a.rating * 10 END) >= 60 THEN '60-69'
          ELSE '60以下'
        END as score_range,
        COUNT(*) as count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY score_range
      ORDER BY score_range DESC
    `;

    const distribution = (await this.sequelize.query(distributionQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return distribution.map(item => ({
      score_range: item.score_range,
      count: parseInt(item.count) || 0,
      percentage:
        Math.round((parseInt(item.count) / totalCount) * 100 * 100) / 100,
    }));
  }

  /**
   * 获取关键词云数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 关键词云数据
   */
  async getKeywordCloud(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 获取所有评价描述和标签
    const keywordQuery = `
      SELECT
        a.description,
        a.tags,
        a.improvement_suggestions,
        a.most_satisfied_aspect,
        a.needs_improvement_aspect
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
        AND (a.description IS NOT NULL
             OR a.tags IS NOT NULL
             OR a.improvement_suggestions IS NOT NULL
             OR a.most_satisfied_aspect IS NOT NULL
             OR a.needs_improvement_aspect IS NOT NULL)
    `;

    const keywordData = (await this.sequelize.query(keywordQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 简单的关键词提取和统计
    const keywordCount = new Map<string, number>();

    keywordData.forEach(item => {
      // 处理标签
      if (item.tags) {
        try {
          const tags =
            typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags;
          if (Array.isArray(tags)) {
            tags.forEach(tag => {
              if (tag && typeof tag === 'string') {
                keywordCount.set(tag, (keywordCount.get(tag) || 0) + 1);
              }
            });
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }

      // 处理文本描述（简单的关键词提取）
      const textFields = [
        item.description,
        item.improvement_suggestions,
        item.most_satisfied_aspect,
        item.needs_improvement_aspect,
      ];

      textFields.forEach(text => {
        if (text && typeof text === 'string') {
          // 简单的中文关键词提取（这里可以集成更复杂的NLP库）
          const keywords = this.extractKeywords(text);
          keywords.forEach(keyword => {
            keywordCount.set(keyword, (keywordCount.get(keyword) || 0) + 1);
          });
        }
      });
    });

    // 转换为数组并按频次排序
    const keywordArray = Array.from(keywordCount.entries())
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // 取前50个关键词

    return keywordArray;
  }

  /**
   * 简单的关键词提取方法
   * @param text 文本
   * @returns 关键词数组
   */
  private extractKeywords(text: string): string[] {
    // 这是一个简化的关键词提取方法
    // 在实际项目中，建议使用专业的中文分词和关键词提取库

    // 常见的正面评价词汇
    const positiveKeywords = [
      '认真',
      '负责',
      '专业',
      '耐心',
      '细心',
      '优秀',
      '棒',
      '好',
      '满意',
      '喜欢',
      '推荐',
      '赞',
      '不错',
      '很好',
      '非常好',
      '教学',
      '方法',
      '互动',
      '生动',
      '有趣',
      '清楚',
      '明白',
      '理解',
      '进步',
      '提高',
    ];

    // 常见的负面评价词汇
    const negativeKeywords = [
      '不好',
      '差',
      '不满意',
      '不喜欢',
      '问题',
      '困难',
      '不清楚',
      '不明白',
      '改进',
      '提升',
      '加强',
      '注意',
      '希望',
      '建议',
      '需要',
      '应该',
    ];

    // 教学相关词汇
    const teachingKeywords = [
      '教学',
      '课堂',
      '作业',
      '考试',
      '学习',
      '知识',
      '技能',
      '能力',
      '方法',
      '技巧',
      '经验',
      '指导',
      '帮助',
      '支持',
      '鼓励',
      '激励',
    ];

    const allKeywords = [
      ...positiveKeywords,
      ...negativeKeywords,
      ...teachingKeywords,
    ];
    const foundKeywords: string[] = [];

    allKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        foundKeywords.push(keyword);
      }
    });

    return foundKeywords;
  }

  /**
   * 获取趋势分析数据
   * @param queryDto 查询条件
   * @returns 趋势分析数据
   */
  async getTrendAnalysis(queryDto: TrendAnalysisQueryDTO): Promise<any> {
    const {
      sso_school_code,
      start_month,
      end_month,
      sso_teacher_id,
      analysis_type,
    } = queryDto;

    if (analysis_type === 'teacher' && sso_teacher_id) {
      return await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    } else {
      return await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }
  }
}
